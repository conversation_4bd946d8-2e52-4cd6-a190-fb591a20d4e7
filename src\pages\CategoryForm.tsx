import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { ArrowLeft, Save } from 'lucide-react';
import config from '../config';
import { fetchWithSession, getCurrentUser } from '../utils/api';

interface CategoryFormData {
  id?: string;
  label: string;
  value: string;
  icon: string;
  count?: number;
}

const EMOJI_OPTIONS = [
  '🍽️', '🍞', '🥐', '🥖', '🥨', '🥯', '🍩', '🍪', '🎂', '🧁',
  '🥮', '🍰', '🍫', '🍬', '🍭', '🍮', '🍯', '🍦', '🍧', '🍨',
  '🍺', '🍻', '🥂', '🍷', '🥃', '🍸', '🍹', '🧃', '🧉', '🧊',
  '🥤', '☕', '🍵', '🍶', '🥛', '🍼', '🍴', '🍽️', '🥄', '🔪',
  '🍳', '🥚', '🧀', '🥓', '🥩', '🍗', '🍖', '🌭', '🍔', '🍟',
  '🍕', '🥪', '🌮', '🌯', '🥙', '🧆', '🥘', '🍲', '🥣', '🥗',
  '🍿', '🧈', '🧂', '🥫', '🍱', '🍘', '🍙', '🍚', '🍛', '🍜',
  '🍝', '🍠', '🍢', '🍣', '🍤', '🍥', '🥮', '🍡', '🥟', '🥠',
  '🥡', '🦪', '🍓', '🍇', '🍈', '🍉', '🍊', '🍋', '🍌', '🍍',
  '🥭', '🍎', '🍏', '🍐', '🍑', '🍒', '🍅', '🍆', '🌽', '🌶️',
  '🍄', '🥑', '🥒', '🥬', '🥦', '🥔', '🧄', '🧅', '🥕', '🌱'
];

const CategoryForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEditMode = id !== undefined;

  const [formData, setFormData] = useState<CategoryFormData>({
    label: '',
    value: '',
    icon: '',
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (isEditMode) {
      const fetchCategory = async () => {
        setIsLoading(true);
        try {
          // Dapatkan user_id dari user yang sedang login
          const currentUser = getCurrentUser();
          const userId = currentUser?.id;

          const response = await fetchWithSession(`${config.apiUrl}/categories/${id}?user_id=${userId}`);
          if (response && response.ok) {
            const categoryData = await response.json();
            setFormData({
              label: categoryData.label,
              value: categoryData.value,
              icon: categoryData.icon,
              count: categoryData.count || 0
            });
          } else {
            navigate('/categories');
          }
        } catch (error) {
          navigate('/categories');
        } finally {
          setIsLoading(false);
        }
      };

      fetchCategory();
    }
  }, [id, isEditMode, navigate]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    if (name === 'label') {
      // Auto-generate value from label (lowercase, no spaces)
      const generatedValue = value.toLowerCase().replace(/\s+/g, '_');
      setFormData(prev => ({
        ...prev,
        [name]: value,
        value: generatedValue
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }

    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleIconSelect = (icon: string) => {
    setFormData(prev => ({ ...prev, icon }));

    // Clear error for icon
    if (errors.icon) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.icon;
        return newErrors;
      });
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.label?.trim()) {
      newErrors.label = 'Nama kategori harus diisi';
    }

    if (!formData.value?.trim()) {
      newErrors.value = 'Kode kategori harus diisi';
    } else if (!/^[a-z0-9_]+$/.test(formData.value)) {
      newErrors.value = 'Kode kategori hanya boleh berisi huruf kecil, angka, dan underscore';
    }

    if (!formData.icon) {
      newErrors.icon = 'Icon harus dipilih';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSaving(true);

    try {
      // Dapatkan user_id dari user yang sedang login
      const currentUser = getCurrentUser();
      const userId = currentUser?.id;

      const url = isEditMode
        ? `${config.apiUrl}/categories/${id}`
        : `${config.apiUrl}/categories`;

      const method = isEditMode ? 'PUT' : 'POST';

      const response = await fetchWithSession(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          value: formData.value,
          label: formData.label,
          icon: formData.icon,
          count: formData.count || 0,
          user_id: userId
        })
      });

      if (response && response.ok) {
        navigate('/categories');
      } else {
        const errorData = await response?.json();
        if (errorData?.error) {
          alert(errorData.error);
        } else {
          alert('Terjadi kesalahan saat menyimpan kategori');
        }
      }
    } catch (error) {
      alert('Terjadi kesalahan saat menyimpan kategori');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center gap-3 mb-6">
        <Link to="/categories" className="p-2 rounded-full hover:bg-neutral-100">
          <ArrowLeft size={20} />
        </Link>
        <h1 className="text-2xl font-bold text-primary-800">
          {isEditMode ? 'Edit Kategori' : 'Tambah Kategori Baru'}
        </h1>
      </div>

      <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
        {isLoading ? (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-primary-500 border-t-transparent"></div>
            <p className="mt-2 text-neutral-600">Memuat data...</p>
          </div>
        ) : (
          <form onSubmit={handleSubmit} autoComplete="off">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <div className="mb-4">
                  <label htmlFor="label" className="block text-sm font-medium text-neutral-700 mb-1">
                    Nama Kategori <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="label"
                    name="label"
                    value={formData.label}
                    onChange={handleChange}
                    className={`w-full p-2 border ${errors.label ? 'border-red-500' : 'border-neutral-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500`}
                    autoComplete="off"
                  />
                  {errors.label && <p className="mt-1 text-sm text-red-500">{errors.label}</p>}
                </div>

                <div className="mb-4">
                  <label htmlFor="value" className="block text-sm font-medium text-neutral-700 mb-1">
                    Kode Kategori <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="value"
                    name="value"
                    value={formData.value}
                    onChange={handleChange}
                    className={`w-full p-2 border ${errors.value ? 'border-red-500' : 'border-neutral-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500`}
                    autoComplete="off"
                  />
                  <p className="mt-1 text-xs text-neutral-500">
                    Kode kategori hanya boleh berisi huruf kecil, angka, dan underscore (_)
                  </p>
                  {errors.value && <p className="mt-1 text-sm text-red-500">{errors.value}</p>}
                </div>
              </div>

              <div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-neutral-700 mb-1">
                    Icon <span className="text-red-500">*</span>
                  </label>
                  <div className="flex items-center mb-2">
                    <div className={`w-12 h-12 flex items-center justify-center text-2xl border border-neutral-300 rounded-md mr-2`}>
                      {formData.icon}
                    </div>
                    <span className="text-sm text-neutral-600">Icon terpilih</span>
                  </div>
                  {errors.icon && <p className="mt-1 text-sm text-red-500">{errors.icon}</p>}

                  <div className="grid grid-cols-8 gap-2 mt-3 max-h-48 overflow-y-auto p-2 border border-neutral-200 rounded-md">
                    {EMOJI_OPTIONS.map((emoji, index) => (
                      <button
                        key={index}
                        type="button"
                        onClick={() => handleIconSelect(emoji)}
                        className={`w-10 h-10 text-xl flex items-center justify-center rounded-md transition-colors ${formData.icon === emoji
                          ? 'bg-primary-100 border-2 border-primary-500'
                          : 'hover:bg-neutral-100'
                          }`}
                      >
                        {emoji}
                      </button>
                    ))}
                  </div>
                </div>

                <div className="mt-6">
                  <button
                    type="submit"
                    className="w-full flex items-center justify-center gap-2 bg-primary-600 text-white py-2 px-4 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                    disabled={isSaving}
                  >
                    {isSaving ? (
                      <div className="inline-block animate-spin rounded-full h-5 w-5 border-4 border-white border-t-transparent"></div>
                    ) : (
                      <>
                        <Save size={18} />
                        <span>{isEditMode ? 'Simpan Perubahan' : 'Tambah Kategori'}</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default CategoryForm;



