const express = require('express');
const router = express.Router();
const pool = require('../config/database');
const Cashier = require('../models/Cashier');

// Get all cashiers
router.get('/', async (req, res) => {
  try {
    const { user_id, branch_id } = req.query;

    if (!user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    let query = `SELECT c.*, b.name as branch_name FROM ${Cashier.$table} c 
                 LEFT JOIN branches b ON c.branch_id = b.id 
                 WHERE c.is_active = 1`;
    const params = [];

    // <PERSON>ka request dari cabang (branch), ambil kasir dari cabang itu sendiri
    if (branch_id) {
      query += ' AND c.branch_id = ?';
      params.push(branch_id);
    } else {
      // Jika request dari admin, ambil kasir miliknya dan semua cabangnya
      query += ' AND (c.user_id = ? OR c.branch_id IN (SELECT id FROM branches WHERE user_id = ?))';
      params.push(user_id, user_id);
    }

    // Add sorting by name
    query += ' ORDER BY c.name ASC';

    const [cashiers] = await pool.query(query, params);
    res.json(cashiers);
  } catch (error) {
    console.error('Error getting cashiers:', error);
    res.status(500).json({ error: 'Failed to get cashiers' });
  }
});

// Get cashier by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { user_id, branch_id } = req.query;

    if (!user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    let query = `SELECT c.*, b.name as branch_name FROM ${Cashier.$table} c 
                 LEFT JOIN branches b ON c.branch_id = b.id 
                 WHERE c.id = ? AND c.is_active = 1`;
    const params = [id];

    // Tambahkan filter berdasarkan user_id atau branch_id
    if (branch_id) {
      query += ' AND c.branch_id = ?';
      params.push(branch_id);
    } else {
      query += ' AND (c.user_id = ? OR c.branch_id IN (SELECT id FROM branches WHERE user_id = ?))';
      params.push(user_id, user_id);
    }

    const [cashiers] = await pool.query(query, params);

    if (cashiers.length === 0) {
      return res.status(404).json({ error: 'Cashier not found' });
    }

    res.json(cashiers[0]);
  } catch (error) {
    console.error('Error getting cashier:', error);
    res.status(500).json({ error: 'Failed to get cashier' });
  }
});

// Create new cashier
router.post('/', async (req, res) => {
  try {
    const { name, email, password, branch_id } = req.body;
    let { user_id } = req.body;

    if (!name || !email || !password || !user_id) {
      return res.status(400).json({ error: 'Name, email, password, and user_id are required' });
    }

    // Validasi email unik di cashiers
    const [existingCashiers] = await pool.query(
      `SELECT * FROM ${Cashier.$table} WHERE email = ? AND is_active = 1`,
      [email]
    );

    if (existingCashiers.length > 0) {
      return res.status(400).json({ error: 'Email sudah digunakan oleh kasir lain' });
    }

    // Validasi email unik di msusers
    const [existingUsers] = await pool.query(
      'SELECT id FROM msusers WHERE email = ?',
      [email]
    );

    if (existingUsers.length > 0) {
      return res.status(400).json({ error: 'Email sudah digunakan oleh pengguna' });
    }

    // Validasi email unik di branches
    const [existingBranches] = await pool.query(
      'SELECT id FROM branches WHERE email = ?',
      [email]
    );

    if (existingBranches.length > 0) {
      return res.status(400).json({ error: 'Email sudah digunakan oleh cabang' });
    }

    // Jika branch_id disediakan, validasi bahwa branch tersebut milik user_id
    // Atau jika user adalah branch, gunakan branch_id mereka sendiri
    let finalBranchId = branch_id;

    // Check if user is a branch (by checking in branches table)
    const [branchUser] = await pool.query(
      'SELECT * FROM branches WHERE id = ? AND is_active = 1',
      [finalBranchId]
    );

    const isBranchUser = branchUser.length > 0;

    if (isBranchUser) {
      // If user is a branch, use their own ID as branch_id
      finalBranchId = branchUser[0].id;
      user_id = branchUser[0].user_id; // Use the user_id of the branch
    } else if (branch_id) {
      // If admin user with branch_id, validate the branch
      const [branches] = await pool.query(
        'SELECT * FROM branches WHERE id = ? AND user_id = ? AND is_active = 1',
        [branch_id, user_id]
      );

      if (branches.length === 0) {
        return res.status(400).json({ error: 'Invalid branch ID' });
      }
    }

    const data = {
      name,
      email,
      password,
      branch_id: finalBranchId || null,
      user_id,
      is_active: 1
    };

    const fields = Object.keys(data).join(', ');
    const placeholders = Object.keys(data).map(() => '?').join(', ');
    const values = Object.values(data);

    const [result] = await pool.query(
      `INSERT INTO ${Cashier.$table} (${fields}) VALUES (${placeholders})`,
      values
    );

    res.status(201).json({ id: result.insertId, ...data });
  } catch (error) {
    console.error('Error creating cashier:', error);
    res.status(500).json({ error: 'Failed to create cashier' });
  }
});

// Update cashier
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, email, password, branch_id, user_id } = req.body;

    if (!user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Validasi kasir ada dan milik user atau cabangnya
    const [existingCashiers] = await pool.query(
      `SELECT * FROM ${Cashier.$table} WHERE id = ? AND 
       (user_id = ? OR branch_id IN (SELECT id FROM branches WHERE user_id = ?)) AND is_active = 1`,
      [id, user_id, user_id]
    );

    if (existingCashiers.length === 0) {
      return res.status(404).json({ error: 'Cashier not found or not authorized' });
    }

    // Validasi email unik jika email diubah
    if (email && email !== existingCashiers[0].email) {
      // Validasi email unik di cashiers
      const [emailExists] = await pool.query(
        `SELECT * FROM ${Cashier.$table} WHERE email = ? AND id != ? AND is_active = 1`,
        [email, id]
      );

      if (emailExists.length > 0) {
        return res.status(400).json({ error: 'Email sudah digunakan oleh kasir lain' });
      }

      // Validasi email unik di msusers
      const [userExists] = await pool.query(
        'SELECT id FROM msusers WHERE email = ?',
        [email]
      );
      if (userExists.length > 0) {
        return res.status(400).json({ error: 'Email sudah digunakan oleh pengguna' });
      }

      // Validasi email unik di branches
      const [branchExists] = await pool.query(
        'SELECT id FROM branches WHERE email = ?',
        [email]
      );
      if (branchExists.length > 0) {
        return res.status(400).json({ error: 'Email sudah digunakan oleh cabang' });
      }
    }

    // Jika branch_id disediakan, validasi bahwa branch tersebut milik user_id
    if (branch_id) {
      const [branches] = await pool.query(
        'SELECT * FROM branches WHERE id = ? AND user_id = ? AND is_active = 1',
        [branch_id, user_id]
      );

      if (branches.length === 0) {
        return res.status(400).json({ error: 'Invalid branch ID' });
      }
    }

    // Buat objek data untuk update
    const data = {};
    if (name) data.name = name;
    if (email) data.email = email;
    if (password) data.password = password;
    if (branch_id !== undefined) data.branch_id = branch_id || null;

    // Jika tidak ada data yang diupdate
    if (Object.keys(data).length === 0) {
      return res.status(400).json({ error: 'No data to update' });
    }

    const setClause = Object.keys(data).map(key => `${key} = ?`).join(', ');
    const values = [...Object.values(data), id];

    const [result] = await pool.query(
      `UPDATE ${Cashier.$table} SET ${setClause} WHERE id = ?`,
      values
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Cashier not found' });
    }

    res.json({ id, ...data });
  } catch (error) {
    console.error('Error updating cashier:', error);
    res.status(500).json({ error: 'Failed to update cashier' });
  }
});

// Delete cashier (soft delete)
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { user_id } = req.query;

    if (!user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Validasi kasir ada dan milik user atau cabangnya
    const [existingCashiers] = await pool.query(
      `SELECT * FROM ${Cashier.$table} WHERE id = ? AND 
       (user_id = ? OR branch_id = ?) AND is_active = 1`,
      [id, user_id, user_id]
    );

    if (existingCashiers.length === 0) {
      return res.status(404).json({ error: 'Cashier not found or not authorized' });
    }

    const [result] = await pool.query(
      `UPDATE ${Cashier.$table} SET is_active = 0 WHERE id = ?`,
      [id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Cashier not found' });
    }

    res.json({ message: 'Cashier deleted successfully' });
  } catch (error) {
    console.error('Error deleting cashier:', error);
    res.status(500).json({ error: 'Failed to delete cashier' });
  }
});

module.exports = router;

