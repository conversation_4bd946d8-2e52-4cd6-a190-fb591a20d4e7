import React, { useState, useEffect } from 'react';
import { Category } from '../types';
import config from '../config';
import { fetchWithSession, getCurrentUser } from '../utils/api';

interface CategoryMenuProps {
  selectedCategory: string;
  onSelectCategory: (category: string) => void;
}

const CategoryMenu: React.FC<CategoryMenuProps> = ({ selectedCategory, onSelectCategory }) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        // Dapatkan user_id dari user yang sedang login
        const currentUser = getCurrentUser();
        const userId = currentUser?.id;
        const userRole = currentUser?.role;

        if (!userId) {
          return;
        }

        // Tambahkan user_id sebagai query parameter
        let url = `${config.apiUrl}/categories?user_id=${userId}`;

        // Jika user adalah cabang, tambahkan parameter branch_id
        if (userRole === 'branch') {
          url += `&branch_id=${userId}`;
        }

        // Jika user adalah kasir, tambahkan parameter cashier_id
        if (userRole === 'cashier') {
          url += `&cashier_id=${userId}`;
        }

        const response = await fetchWithSession(url);

        if (response && response.ok) {
          const data = await response.json();
          setCategories([...data]);
        }
      } catch (error) { }

      setIsLoading(false);
    };

    fetchCategories();
  }, []);

  if (isLoading) {
    return (
      <div className="flex overflow-x-auto gap-3 pb-2 hide-scrollbar">
        {[1, 2, 3, 4].map((i) => (
          <div
            key={i}
            className="flex flex-col items-center justify-center p-3 min-w-[100px] rounded-lg border border-neutral-200 animate-pulse"
          >
            <div className="w-8 h-8 rounded-full bg-neutral-200 mb-1"></div>
            <div className="w-16 h-4 bg-neutral-200 rounded mb-1"></div>
            <div className="w-10 h-3 bg-neutral-200 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="flex overflow-x-auto gap-3 pb-2 hide-scrollbar">
      {categories.map((category) => (
        <button
          key={category.id}
          onClick={() => onSelectCategory(category.value)}
          className={`flex flex-col items-center justify-center p-3 min-w-[100px] rounded-lg border transition-all ${selectedCategory === category.value
            ? 'border-primary-600 bg-primary-50 text-primary-700'
            : 'border-neutral-200 hover:border-primary-300 hover:bg-neutral-50'
            }`}
        >
          <div className="text-2xl mb-1">{category.icon}</div>
          <div className="font-medium text-sm">{category.label}</div>
          <div className="text-xs text-neutral-500">{category.count} items</div>
        </button>
      ))}
    </div>
  );
};

export default CategoryMenu;







