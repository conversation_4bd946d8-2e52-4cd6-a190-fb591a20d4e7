const express = require('express');
const router = express.Router();
const pool = require('../config/database');
const Member = require('../models/Member');

// Get all members
router.get('/', async (req, res) => {
  try {
    const { user_id, cashier_id } = req.query;
    let { branch_id } = req.query;

    // Jika request dari kasir, tentukan branch_id
    if (cashier_id) {
      const [cashierResult] = await pool.query(
        'SELECT branch_id FROM cashiers WHERE id = ?',
        [cashier_id]
      );

      if (cashierResult.length === 0) {
        return res.status(404).json({ error: 'Cashier not found' });
      }

      branch_id = cashierResult[0].branch_id;
    }

    if (!user_id) {
      return res.status(400).json({ error: 'User ID diperlukan' });
    }

    let query = `SELECT * FROM ${Member.$table}`;
    const params = [];

    // Jika request dari cabang (branch), ambil member dari admin (parent) dan cabang itu sendiri
    if (branch_id) {
      // Dapatkan user_id (admin/parent) dari cabang
      const [branchResult] = await pool.query(
        'SELECT user_id FROM branches WHERE id = ?',
        [branch_id]
      );

      if (branchResult.length > 0) {
        const adminId = branchResult[0].user_id;
        query += ' WHERE user_id = ? OR user_id = ?';
        params.push(adminId, user_id);
      } else {
        query += ' WHERE user_id = ?';
        params.push(user_id);
      }
    } else {
      // Jika request dari admin, hanya ambil member miliknya
      query += ' WHERE user_id = ?';
      params.push(user_id);
    }

    query += ' ORDER BY name ASC';

    const [rows] = await pool.query(query, params);
    res.json(rows);
  } catch (error) {
    console.error('Error fetching members:', error);
    res.status(500).json({ error: 'Gagal mengambil data member' });
  }
});

// Get member by ID
router.get('/:id', async (req, res) => {
  try {
    // Jika ID adalah 'new', kembalikan error
    if (req.params.id === 'new') {
      return res.status(404).json({ error: 'Member tidak ditemukan' });
    }

    const { user_id } = req.query;

    let query = `SELECT * FROM ${Member.$table} WHERE id = ?`;
    const params = [req.params.id];

    // Filter berdasarkan user_id jika ada
    if (user_id) {
      query += ' AND user_id = ?';
      params.push(user_id);
    }

    const [rows] = await pool.query(query, params);

    if (rows.length === 0) {
      return res.status(404).json({ error: 'Member tidak ditemukan' });
    }

    res.json(rows[0]);
  } catch (error) {
    console.error('Error fetching member:', error);
    res.status(500).json({ error: 'Gagal mengambil data member' });
  }
});

// Create new member
router.post('/', async (req, res) => {
  try {
    let { name, phone, email, address, birthdate, is_active, notes, user_id, branch_id, cashier_id } = req.body;

    // Jika request dari kasir, tentukan branch_id
    if (cashier_id) {
      const [cashierResult] = await pool.query(
        'SELECT branch_id FROM cashiers WHERE id = ?',
        [cashier_id]
      );

      if (cashierResult.length === 0) {
        return res.status(404).json({ error: 'Cashier tidak ditemukan' });
      }

      branch_id = cashierResult[0].branch_id;
    }

    // Jika ada branch_id, ambil user_id admin (pusat) dari tabel branches
    if (branch_id) {
      const [branchResult] = await pool.query(
        'SELECT user_id FROM branches WHERE id = ?',
        [branch_id]
      );
      if (branchResult.length > 0) {
        user_id = branchResult[0].user_id;
      } else {
        return res.status(400).json({ error: 'Branch tidak ditemukan' });
      }
    }

    // Validasi input
    if (!name || !phone || !user_id) {
      return res.status(400).json({ error: 'Nama, nomor telepon, dan user_id harus diisi' });
    }

    // Cek apakah nomor telepon sudah terdaftar untuk user_id yang sama
    const [existingMembers] = await pool.query(
      `SELECT id FROM ${Member.$table} WHERE phone = ? AND user_id = ?`,
      [phone, user_id]
    );

    if (existingMembers.length > 0) {
      return res.status(400).json({ error: 'Nomor telepon sudah terdaftar' });
    }

    // Siapkan data untuk disimpan
    const data = {
      name,
      phone,
      email: email || null,
      address: address || null,
      birthdate: birthdate || null,
      is_active: is_active === 1 || is_active === true ? 1 : 0,
      notes: notes || null,
      user_id,
      created_at: new Date(),
      updated_at: new Date()
    };

    // Buat query dan parameter
    const fields = Object.keys(data).join(', ');
    const placeholders = Object.keys(data).map(() => '?').join(', ');
    const values = Object.values(data);

    const [result] = await pool.query(
      `INSERT INTO ${Member.$table} (${fields}) VALUES (${placeholders})`,
      values
    );

    if (result.affectedRows === 0) {
      return res.status(500).json({ error: 'Gagal menambahkan member' });
    }

    res.status(201).json({
      id: result.insertId,
      ...data
    });
  } catch (error) {
    console.error('Error creating member:', error);
    res.status(500).json({ error: 'Gagal menambahkan member' });
  }
});

// Update member
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, phone, email, address, birthdate, is_active, notes, user_id } = req.body;

    // Validasi input
    if (!name || !phone || !user_id) {
      return res.status(400).json({ error: 'Nama, nomor telepon, dan user_id harus diisi' });
    }

    // Cek apakah member ada dan milik user yang sama
    const [existingRows] = await pool.query(
      `SELECT * FROM ${Member.$table} WHERE id = ? AND user_id = ?`,
      [id, user_id]
    );

    if (existingRows.length === 0) {
      return res.status(404).json({ error: 'Member tidak ditemukan atau tidak berwenang' });
    }

    // Cek apakah nomor telepon sudah digunakan oleh member lain dengan user_id yang sama
    const [existingPhones] = await pool.query(
      `SELECT id FROM ${Member.$table} WHERE phone = ? AND id != ? AND user_id = ?`,
      [phone, id, user_id]
    );

    if (existingPhones.length > 0) {
      return res.status(400).json({ error: 'Nomor telepon sudah digunakan oleh member lain' });
    }

    // Siapkan data untuk diupdate
    const data = {
      name,
      phone,
      email: email || null,
      address: address || null,
      birthdate: birthdate || null,
      is_active: is_active === 1 || is_active === true ? 1 : 0,
      notes: notes || null,
      updated_at: new Date()
    };

    // Buat query dan parameter
    const setClause = Object.keys(data).map(key => `${key} = ?`).join(', ');
    const values = [...Object.values(data), id];

    const [result] = await pool.query(
      `UPDATE ${Member.$table} SET ${setClause} WHERE id = ?`,
      values
    );

    if (result.affectedRows === 0) {
      return res.status(500).json({ error: 'Gagal mengupdate member' });
    }

    res.json({
      id: parseInt(id),
      ...data,
      user_id
    });
  } catch (error) {
    console.error('Error updating member:', error);
    res.status(500).json({ error: 'Gagal mengupdate member' });
  }
});

// Delete member
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { user_id } = req.query;

    if (!user_id) {
      return res.status(400).json({ error: 'User ID diperlukan' });
    }

    // Cek apakah member ada dan milik user yang sama
    const [existingRows] = await pool.query(
      `SELECT * FROM ${Member.$table} WHERE id = ? AND user_id = ?`,
      [id, user_id]
    );

    if (existingRows.length === 0) {
      return res.status(404).json({ error: 'Member tidak ditemukan atau tidak berwenang' });
    }

    // Hapus member
    const [result] = await pool.query(
      `DELETE FROM ${Member.$table} WHERE id = ?`,
      [id]
    );

    if (result.affectedRows === 0) {
      return res.status(500).json({ error: 'Gagal menghapus member' });
    }

    res.json({ message: 'Member berhasil dihapus' });
  } catch (error) {
    console.error('Error deleting member:', error);
    res.status(500).json({ error: 'Gagal menghapus member' });
  }
});

module.exports = router;


