const express = require('express');
const router = express.Router();
const pool = require('../config/database');

// Get all branches
router.get('/', async (req, res) => {
  try {
    const userId = req.query.user_id;

    let query = 'SELECT * FROM branches';
    let params = [];

    // Jika ada user_id, filter cabang berdasarkan user_id
    if (userId) {
      query += ' WHERE user_id = ?';
      params.push(userId);
    }

    // Add sorting by name
    query += ' ORDER BY name ASC';

    const [rows] = await pool.query(query, params);
    res.json(rows);
  } catch (error) {
    console.error('Error fetching branches:', error);
    res.status(500).json({ error: 'Failed to fetch branches' });
  }
});

// Get branch by ID
router.get('/:id', async (req, res) => {
  try {
    const [rows] = await pool.query('SELECT * FROM branches WHERE id = ?', [req.params.id]);
    if (rows.length === 0) {
      return res.status(404).json({ error: 'Branch not found' });
    }
    res.json(rows[0]);
  } catch (error) {
    console.error('Error fetching branch:', error);
    res.status(500).json({ error: 'Failed to fetch branch' });
  }
});

// Create new branch
router.post('/', async (req, res) => {
  const { name, address, phone, manager, is_active, email, password, user_id } = req.body;

  if (!name || !address || !phone || !manager || !email || !password) {
    return res.status(400).json({ error: 'All fields are required' });
  }

  try {
    // Cek apakah email sudah digunakan di tabel branches
    const [existingBranches] = await pool.query(
      'SELECT id FROM branches WHERE email = ?',
      [email]
    );

    if (existingBranches.length > 0) {
      return res.status(400).json({ error: 'Email sudah digunakan oleh cabang lain' });
    }

    // Cek apakah email sudah digunakan di tabel msusers
    const [existingUsers] = await pool.query(
      'SELECT id FROM msusers WHERE email = ?',
      [email]
    );

    if (existingUsers.length > 0) {
      return res.status(400).json({ error: 'Email sudah digunakan oleh pengguna' });
    }

    // Cek apakah email sudah digunakan di tabel cashiers
    const [existingCashiers] = await pool.query(
      'SELECT id FROM cashiers WHERE email = ? AND is_active = 1',
      [email]
    );

    if (existingCashiers.length > 0) {
      return res.status(400).json({ error: 'Email sudah digunakan oleh kasir' });
    }

    const [result] = await pool.query(
      'INSERT INTO branches (name, address, phone, manager, is_active, email, password, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
      [name, address, phone, manager, is_active || 1, email, password, user_id || null]
    );

    res.status(201).json({
      id: result.insertId,
      name,
      address,
      phone,
      manager,
      is_active: is_active || 1,
      email,
      user_id: user_id || null
    });
  } catch (error) {
    console.error('Error creating branch:', error);
    res.status(500).json({ error: 'Failed to create branch' });
  }
});

// Update branch
router.put('/:id', async (req, res) => {
  const { name, address, phone, manager, is_active, email, password, user_id } = req.body;

  if (!name || !address || !phone || !manager || !email) {
    return res.status(400).json({ error: 'Required fields missing' });
  }

  try {
    // Cek apakah email sudah digunakan oleh cabang lain
    const [existingBranches] = await pool.query(
      'SELECT id FROM branches WHERE email = ? AND id != ?',
      [email, req.params.id]
    );

    if (existingBranches.length > 0) {
      return res.status(400).json({ error: 'Email sudah digunakan oleh cabang lain' });
    }

    // Cek apakah email sudah digunakan di tabel msusers
    const [existingUsers] = await pool.query(
      'SELECT id FROM msusers WHERE email = ?',
      [email]
    );

    if (existingUsers.length > 0) {
      return res.status(400).json({ error: 'Email sudah digunakan oleh pengguna' });
    }

    // Cek apakah email sudah digunakan di tabel cashiers
    const [existingCashiers] = await pool.query(
      'SELECT id FROM cashiers WHERE email = ? AND is_active = 1',
      [email]
    );

    if (existingCashiers.length > 0) {
      return res.status(400).json({ error: 'Email sudah digunakan oleh kasir' });
    }

    // Buat query update
    let query = 'UPDATE branches SET name = ?, address = ?, phone = ?, manager = ?, is_active = ?, email = ?, user_id = ?';
    let params = [name, address, phone, manager, is_active || 1, email, user_id || null];

    // Tambahkan password ke query jika ada
    if (password) {
      query += ', password = ?';
      params.push(password);
    }

    // Tambahkan WHERE clause
    query += ' WHERE id = ?';
    params.push(req.params.id);

    await pool.query(query, params);

    res.json({
      id: parseInt(req.params.id),
      name,
      address,
      phone,
      manager,
      is_active: is_active || 1,
      email,
      user_id: user_id || null
    });
  } catch (error) {
    console.error('Error updating branch:', error);
    res.status(500).json({ error: 'Failed to update branch' });
  }
});

// Delete branch - dengan validasi user_id
router.delete('/:id', async (req, res) => {
  const branchId = req.params.id;
  const userId = req.query.user_id;

  try {
    // Cek apakah cabang ada dan dibuat oleh user yang sama
    const [branch] = await pool.query(
      'SELECT * FROM branches WHERE id = ?',
      [branchId]
    );

    if (branch.length === 0) {
      return res.status(404).json({ error: 'Cabang tidak ditemukan' });
    }

    // Validasi apakah user_id yang menghapus sama dengan user_id yang membuat cabang
    if (branch[0].user_id && branch[0].user_id !== parseInt(userId)) {
      return res.status(403).json({ error: 'Anda tidak memiliki izin untuk menghapus cabang ini' });
    }

    await pool.query('DELETE FROM branches WHERE id = ?', [branchId]);
    res.json({ message: 'Branch deleted successfully' });
  } catch (error) {
    console.error('Error deleting branch:', error);
    res.status(500).json({ error: 'Failed to delete branch' });
  }
});

module.exports = router;




