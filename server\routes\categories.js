const express = require('express');
const router = express.Router();
const pool = require('../config/database');

// Get all categories
router.get('/', async (req, res) => {
  try {
    const { user_id, cashier_id } = req.query;
    let { branch_id } = req.query;

    // If the request is from a cashier, determine the branch_id
    if (cashier_id) {
      const [cashierResult] = await pool.query(
        'SELECT branch_id FROM cashiers WHERE id = ?',
        [cashier_id]
      );

      if (cashierResult.length === 0) {
        return res.status(404).json({ error: 'Cashier not found' });
      }

      branch_id = cashierResult[0].branch_id;
    }

    // Jika tidak ada user_id, kembalikan error
    if (!user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    let query, params;

    // Jika request dari cabang (branch), ambil kategori dari admin (parent) dan cabang itu sendiri
    if (branch_id) {
      // Dapatkan user_id (admin/parent) dari cabang
      const [branchResult] = await pool.query(
        'SELECT user_id FROM branches WHERE id = ?',
        [branch_id]
      );

      if (branchResult.length === 0) {
        return res.status(404).json({ error: 'Branch not found' });
      }

      const adminId = branchResult[0].user_id;

      // Ambil kategori milik admin dan cabang
      query = 'SELECT * FROM categories WHERE user_id = ?';
      params = [adminId];
    } else {
      // Jika request dari admin, hanya ambil kategori miliknya
      query = 'SELECT * FROM categories WHERE user_id = ?';
      params = [user_id];
    }

    query += ' ORDER BY label ASC';

    // Ambil kategori dari database
    const [rows] = await pool.query(query, params);

    // Pastikan kategori 'all' selalu ada
    const allCategoryExists = rows.some(cat => cat.value === 'all');

    if (!allCategoryExists) {
      // Tambahkan kategori 'all' jika belum ada, dengan user_id yang sesuai
      rows.unshift({
        id: 0,
        value: 'all',
        label: 'All Menu',
        icon: '🍽️',
        count: 0,
        user_id: parseInt(user_id)
      });
    }

    // Hitung jumlah produk untuk setiap kategori
    let productQuery = 'SELECT category, COUNT(*) as count FROM products';
    const productParams = [];

    if (branch_id) {
      // Jika cabang, ambil produk milik admin dan cabang
      const [branchResult] = await pool.query(
        'SELECT user_id FROM branches WHERE id = ?',
        [branch_id]
      );

      if (branchResult.length > 0) {
        const adminId = branchResult[0].user_id;
        productQuery += ' WHERE (user_id = ? OR user_id = ?)';
        productParams.push(adminId, user_id);
      } else {
        productQuery += ' WHERE user_id = ?';
        productParams.push(user_id);
      }
    } else {
      // Jika admin, hanya ambil produk miliknya
      productQuery += ' WHERE user_id = ?';
      productParams.push(user_id);
    }

    productQuery += ' AND is_active = 1 GROUP BY category';

    const [products] = await pool.query(productQuery, productParams);

    // Buat map untuk menyimpan jumlah produk per kategori
    const categoryCounts = {};
    products.forEach(product => {
      categoryCounts[product.category] = product.count;
    });

    // Hitung total produk untuk kategori 'all'
    let totalProductQuery = 'SELECT COUNT(*) as count FROM products';
    const totalProductParams = [];

    if (branch_id) {
      // Jika cabang, hitung produk milik admin dan cabang
      const [branchResult] = await pool.query(
        'SELECT user_id FROM branches WHERE id = ?',
        [branch_id]
      );

      if (branchResult.length > 0) {
        const adminId = branchResult[0].user_id;
        totalProductQuery += ' WHERE (user_id = ? OR user_id = ?)';
        totalProductParams.push(adminId, user_id);
      } else {
        totalProductQuery += ' WHERE user_id = ?';
        totalProductParams.push(user_id);
      }
    } else {
      // Jika admin, hanya hitung produk miliknya
      totalProductQuery += ' WHERE user_id = ?';
      totalProductParams.push(user_id);
    }

    totalProductQuery += ' AND is_active = 1';

    const [totalProducts] = await pool.query(totalProductQuery, totalProductParams);
    const totalCount = totalProducts[0].count;

    // Update jumlah produk untuk setiap kategori
    const categoriesWithCounts = rows.map(category => {
      if (category.value === 'all') {
        return { ...category, count: totalCount };
      }
      return { ...category, count: categoryCounts[category.value] || 0 };
    });

    res.json(categoriesWithCounts);
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({ error: 'Failed to fetch categories' });
  }
});

// Get category by ID
router.get('/:id', async (req, res) => {
  try {
    const { user_id } = req.query;

    let query = 'SELECT * FROM categories WHERE id = ?';
    const params = [req.params.id];

    // Filter berdasarkan user_id jika ada
    if (user_id) {
      query += ' AND user_id = ?';
      params.push(user_id);
    }

    const [rows] = await pool.query(query, params);
    if (rows.length === 0) {
      return res.status(404).json({ error: 'Category not found' });
    }
    res.json(rows[0]);
  } catch (error) {
    console.error('Error fetching category:', error);
    res.status(500).json({ error: 'Failed to fetch category' });
  }
});

// Create new category
router.post('/', async (req, res) => {
  const { value, label, icon, count, user_id } = req.body;

  if (!value || !label || !user_id) {
    return res.status(400).json({ error: 'Value, label, and user_id are required' });
  }

  try {
    const [result] = await pool.query(
      'INSERT INTO categories (value, label, icon, count, user_id) VALUES (?, ?, ?, ?, ?)',
      [value, label, icon || '', count || 0, user_id]
    );

    res.status(201).json({ id: result.insertId, ...req.body });
  } catch (error) {
    console.error('Error creating category:', error);
    res.status(500).json({ error: 'Failed to create category' });
  }
});

// Update category
router.put('/:id', async (req, res) => {
  const { value, label, icon, count, user_id } = req.body;

  if (!value || !label || !user_id) {
    return res.status(400).json({ error: 'Value, label, and user_id are required' });
  }

  try {
    // Cek apakah kategori ada dan milik user yang sama
    const [categories] = await pool.query(
      'SELECT * FROM categories WHERE id = ? AND user_id = ?',
      [req.params.id, user_id]
    );

    if (categories.length === 0) {
      return res.status(404).json({ error: 'Category not found or not authorized' });
    }

    await pool.query(
      'UPDATE categories SET value = ?, label = ?, icon = ?, count = ? WHERE id = ?',
      [value, label, icon || '', count || 0, req.params.id]
    );

    res.json({ id: req.params.id, ...req.body });
  } catch (error) {
    console.error('Error updating category:', error);
    res.status(500).json({ error: 'Failed to update category' });
  }
});

// Delete category
router.delete('/:id', async (req, res) => {
  try {
    const { user_id } = req.query;

    if (!user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Cek apakah kategori ada dan milik user yang sama
    const [categories] = await pool.query(
      'SELECT * FROM categories WHERE id = ? AND user_id = ?',
      [req.params.id, user_id]
    );

    if (categories.length === 0) {
      return res.status(404).json({ error: 'Category not found or not authorized' });
    }

    // Jangan izinkan menghapus kategori 'all'
    if (categories[0].value === 'all') {
      return res.status(400).json({ error: 'Cannot delete the "all" category' });
    }

    await pool.query('DELETE FROM categories WHERE id = ?', [req.params.id]);
    res.json({ message: 'Category deleted successfully' });
  } catch (error) {
    console.error('Error deleting category:', error);
    res.status(500).json({ error: 'Failed to delete category' });
  }
});

module.exports = router;


