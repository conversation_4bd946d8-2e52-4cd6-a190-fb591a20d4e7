import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Save } from 'lucide-react';
import { fetchWithSession, getCurrentUser } from '../utils/api';
import config from '../config';

interface Branch {
  id: number;
  name: string;
}

const CashierForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEditMode = !!id;

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    branch_id: '',
  });
  const [branches, setBranches] = useState<Branch[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get current user info
  const currentUser = getCurrentUser();
  const isAdmin = currentUser?.role === 'admin';
  const userId = currentUser?.id;

  // Fetch branches for admin
  useEffect(() => {
    if (isAdmin) {
      const fetchBranches = async () => {
        try {
          const response = await fetchWithSession(`${config.apiUrl}/branches?user_id=${userId}`);
          if (response && response.ok) {
            const data = await response.json();
            setBranches(data);
          }
        } catch (error) {
          console.error('Error fetching branches:', error);
          setError('Gagal memuat data cabang');
        }
      };

      fetchBranches();
    }
  }, [isAdmin, userId]);

  // Fetch cashier data if in edit mode
  useEffect(() => {
    if (isEditMode && id) {
      const fetchCashier = async () => {
        setIsLoading(true);
        try {
          // Build URL with appropriate parameters based on user role
          let url = `${config.apiUrl}/cashiers/${id}?user_id=${userId}`;

          // If user is a branch, add branch_id parameter
          if (!isAdmin) {
            url += `&branch_id=${userId}`;
          }

          const response = await fetchWithSession(url);
          if (response && response.ok) {
            const data = await response.json();
            setFormData({
              name: data.name || '',
              email: data.email || '',
              password: '', // Don't populate password for security
              branch_id: data.branch_id ? String(data.branch_id) : '',
            });
          } else {
            setError('Kasir tidak ditemukan atau Anda tidak memiliki akses');
          }
        } catch (error) {
          console.error('Error fetching cashier:', error);
          setError('Gagal memuat data kasir');
        } finally {
          setIsLoading(false);
        }
      };

      fetchCashier();
    }
  }, [isEditMode, id, userId, isAdmin]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const payload = {
        ...formData,
        user_id: userId,
        // If branch user, set branch_id to the branch user's ID
        branch_id: !isAdmin ? userId : (formData.branch_id || null),
      };

      const url = isEditMode
        ? `${config.apiUrl}/cashiers/${id}`
        : `${config.apiUrl}/cashiers`;

      const method = isEditMode ? 'PUT' : 'POST';

      const response = await fetchWithSession(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (response && response.ok) {
        navigate('/cashier-management');
      } else {
        const errorData = await response?.json();
        setError(errorData.error || 'Gagal menyimpan data kasir');
      }
    } catch (error) {
      console.error('Error saving cashier:', error);
      setError('Terjadi kesalahan saat menyimpan data');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center gap-3 mb-6">
        <Link to="/cashier-management" className="p-2 rounded-full hover:bg-neutral-100">
          <ArrowLeft size={20} />
        </Link>
        <h1 className="text-2xl font-bold text-primary-800">
          {isEditMode ? 'Edit Kasir' : 'Tambah Kasir Baru'}
        </h1>
      </div>

      <div className="bg-white rounded-xl shadow-sm p-6 w-full">
        {isLoading && !isEditMode ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>
        ) : (
          <form onSubmit={handleSubmit}>
            {error && (
              <div className="mb-6 p-4 bg-red-50 text-red-700 rounded-md">
                {error}
              </div>
            )}

            <div className="mb-4">
              <label htmlFor="name" className="block text-sm font-medium text-neutral-700 mb-1">
                Nama Kasir <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className="w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                required
                autoComplete="off"
              />
            </div>

            <div className="mb-4">
              <label htmlFor="email" className="block text-sm font-medium text-neutral-700 mb-1">
                Email <span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className="w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                required
                autoComplete="off"
              />
            </div>

            <div className="mb-4">
              <label htmlFor="password" className="block text-sm font-medium text-neutral-700 mb-1">
                Password <span className="text-red-500">{!isEditMode && '*'}</span> {isEditMode && '(Kosongkan jika tidak ingin mengubah)'}
              </label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                className="w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                required={!isEditMode}
                autoComplete="off"
              />
            </div>

            {/* Branch selection field - only visible for admin users */}
            {isAdmin && (
              <div className="mb-6">
                <label htmlFor="branch_id" className="block text-sm font-medium text-neutral-700 mb-1">
                  Cabang
                </label>
                <select
                  id="branch_id"
                  name="branch_id"
                  value={formData.branch_id}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  autoComplete="off"
                >
                  <option value="">Admin (Pusat)</option>
                  {branches.map(branch => (
                    <option key={branch.id} value={branch.id}>
                      {branch.name}
                    </option>
                  ))}
                </select>
              </div>
            )}

            <div className="flex justify-end">
              <button
                type="submit"
                disabled={isLoading}
                className="flex items-center gap-2 bg-primary-600 text-white px-6 py-2 rounded-md hover:bg-primary-700 transition-colors disabled:bg-primary-400"
              >
                {isLoading ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                ) : (
                  <Save size={18} />
                )}
                <span>Simpan</span>
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default CashierForm;




