const express = require('express');
const router = express.Router();
const pool = require('../config/database');

// Login route
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Check if it's an admin login
    const [adminUsers] = await pool.query(
      'SELECT id, name, email, role FROM msusers WHERE email = ? AND password = ? AND is_active = 1',
      [email, password]
    );

    if (adminUsers.length > 0) {
      const user = adminUsers[0];
      return res.json({
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role
      });
    }

    // Check if it's a branch login
    const [branches] = await pool.query(
      'SELECT id, name, email FROM branches WHERE email = ? AND password = ? AND is_active = 1',
      [email, password]
    );

    if (branches.length > 0) {
      const branch = branches[0];
      return res.json({
        id: branch.id,
        name: branch.name,
        email: branch.email,
        role: 'branch'
      });
    }

    // Check if it's a cashier login
    const [cashiers] = await pool.query(
      'SELECT id, name, email FROM cashiers WHERE email = ? AND password = ? AND is_active = 1',
      [email, password]
    );

    if (cashiers.length > 0) {
      const cashier = cashiers[0];
      return res.json({
        id: cashier.id,
        name: cashier.name,
        email: cashier.email,
        role: 'cashier'
      });
    }

    // If no user found
    res.status(401).json({ error: 'Invalid email or password' });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Login failed' });
  }
});

// Change password route
router.post('/change-password', async (req, res) => {
  try {
    const { userId, userType, currentPassword, newPassword } = req.body;

    // Validasi input
    if (!userId || !userType || !currentPassword || !newPassword) {
      return res.status(400).json({ error: 'Semua field harus diisi' });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({ error: 'Password baru minimal 6 karakter' });
    }

    // Cek apakah user ada dan password lama benar
    let userExists = false;

    if (userType === 'admin') {
      // Cek di tabel msusers untuk admin
      const [users] = await pool.query(
        'SELECT id FROM msusers WHERE id = ? AND password = ? AND is_active = 1',
        [userId, currentPassword]
      );
      userExists = users.length > 0;

      if (userExists) {
        // Update password admin
        await pool.query(
          'UPDATE msusers SET password = ? WHERE id = ?',
          [newPassword, userId]
        );
      }
    } else if (userType === 'branch') {
      // Cek di tabel branches untuk cabang
      const [branches] = await pool.query(
        'SELECT id FROM branches WHERE id = ? AND password = ? AND is_active = 1',
        [userId, currentPassword]
      );
      userExists = branches.length > 0;

      if (userExists) {
        // Update password cabang
        await pool.query(
          'UPDATE branches SET password = ? WHERE id = ?',
          [newPassword, userId]
        );
      }
    } else if (userType === 'cashier') {
      // Cek di tabel cashiers untuk kasir
      const [cashiers] = await pool.query(
        'SELECT id FROM cashiers WHERE id = ? AND password = ? AND is_active = 1',
        [userId, currentPassword]
      );
      userExists = cashiers.length > 0;

      if (userExists) {
        // Update password kasir
        await pool.query(
          'UPDATE cashiers SET password = ? WHERE id = ?',
          [newPassword, userId]
        );
      }
    }

    if (!userExists) {
      return res.status(401).json({ error: 'Password lama tidak sesuai' });
    }

    res.json({ message: 'Password berhasil diubah' });
  } catch (error) {
    console.error('Error changing password:', error);
    res.status(500).json({ error: 'Gagal mengubah password' });
  }
});

module.exports = router;




