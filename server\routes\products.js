const express = require('express');
const router = express.Router();
const pool = require('../config/database');
const Product = require('../models/Product');

// Get all products
router.get('/', async (req, res) => {
  try {
    const { user_id, cashier_id } = req.query;
    let { branch_id } = req.query;

    // Jika request dari kasir, tentukan branch_id
    if (cashier_id) {
      const [cashierResult] = await pool.query(
        'SELECT branch_id FROM cashiers WHERE id = ?',
        [cashier_id]
      );

      if (cashierResult.length === 0) {
        return res.status(404).json({ error: 'Cashier not found' });
      }

      branch_id = cashierResult[0].branch_id;
    }

    if (!user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    let query = `SELECT * FROM ${Product.$table} WHERE is_active = 1`;
    const params = [];

    // <PERSON><PERSON> request dari cabang (branch), ambil produk dari admin (parent) dan cabang itu sendiri
    if (branch_id) {
      // Dapatkan user_id (admin/parent) dari cabang
      const [branchResult] = await pool.query(
        'SELECT user_id FROM branches WHERE id = ?',
        [branch_id]
      );

      if (branchResult.length > 0) {
        const adminId = branchResult[0].user_id;
        query += ` AND (user_id = ? OR user_id = ?)`;
        params.push(adminId, user_id);
      } else {
        query += ` AND user_id = ?`;
        params.push(user_id);
      }
    } else {
      // Jika request dari admin, hanya ambil produk miliknya
      query += ` AND user_id = ?`;
      params.push(user_id);
    }

    // Add sorting by name
    query += ` ORDER BY name ASC`;

    const [products] = await pool.query(query, params);
    res.json(products);
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({ error: 'Failed to fetch products' });
  }
});

// Get product by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { user_id } = req.query;

    let query = `SELECT * FROM ${Product.$table} WHERE id = ? AND is_active = 1`;
    const params = [id];

    // Filter berdasarkan user_id jika ada
    if (user_id) {
      query += ` AND user_id = ?`;
      params.push(user_id);
    }

    const [products] = await pool.query(query, params);

    if (products.length === 0) {
      return res.status(404).json({ error: 'Product not found' });
    }

    res.json(products[0]);
  } catch (error) {
    console.error('Error fetching product:', error);
    res.status(500).json({ error: 'Failed to fetch product' });
  }
});

// Create new product
router.post('/', async (req, res) => {
  try {
    const { name, price, cost_price, image, category, category_label, user_id } = req.body;

    // Validasi input
    if (!name || !price || !image || !category || !user_id) {
      return res.status(400).json({ error: 'All fields are required' });
    }

    const [result] = await pool.query(
      `INSERT INTO ${Product.$table} (name, price, cost_price, image, category, category_label, user_id, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, 1)`,
      [name, price, cost_price || 0, image, category, category_label, user_id]
    );

    res.status(201).json({
      id: result.insertId,
      name,
      price,
      cost_price,
      image,
      category,
      category_label,
      user_id,
      is_active: 1
    });
  } catch (error) {
    console.error('Error creating product:', error);
    res.status(500).json({ error: 'Failed to create product' });
  }
});

// Update product
router.put('/:id', async (req, res) => {
  try {
    const { name, price, cost_price, image, category, category_label, user_id } = req.body;
    const productId = req.params.id;

    // Validasi input
    if (!name || !price || !image || !category || !user_id) {
      return res.status(400).json({ error: 'All fields are required' });
    }

    // Cek apakah produk ada dan milik user yang sama
    const [products] = await pool.query(
      `SELECT * FROM ${Product.$table} WHERE id = ? AND is_active = 1 AND user_id = ?`,
      [productId, user_id]
    );

    if (products.length === 0) {
      return res.status(404).json({ error: 'Product not found or not authorized' });
    }

    await pool.query(
      `UPDATE ${Product.$table} SET name = ?, price = ?, cost_price = ?, image = ?, category = ?, category_label = ? WHERE id = ?`,
      [name, price, cost_price || 0, image, category, category_label, productId]
    );

    res.json({
      id: productId,
      name,
      price,
      cost_price,
      image,
      category,
      category_label,
      user_id,
      is_active: 1
    });
  } catch (error) {
    console.error('Error updating product:', error);
    res.status(500).json({ error: 'Failed to update product' });
  }
});

// Delete product (soft delete)
router.delete('/:id', async (req, res) => {
  try {
    const productId = req.params.id;
    const { user_id } = req.query;

    // Cek apakah produk ada dan milik user yang sama
    const [products] = await pool.query(
      `SELECT * FROM ${Product.$table} WHERE id = ? AND is_active = 1 AND user_id = ?`,
      [productId, user_id]
    );

    if (products.length === 0) {
      return res.status(404).json({ error: 'Product not found or not authorized' });
    }

    await pool.query(
      `UPDATE ${Product.$table} SET is_active = 0 WHERE id = ?`,
      [productId]
    );

    res.json({ message: 'Product deleted successfully' });
  } catch (error) {
    console.error('Error deleting product:', error);
    res.status(500).json({ error: 'Failed to delete product' });
  }
});

module.exports = router;

